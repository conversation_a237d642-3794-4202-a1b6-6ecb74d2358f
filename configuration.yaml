# Loads default set of integrations. Do not remove.
default_config:

# Load frontend themes from the themes folder
frontend:
  themes: !include_dir_merge_named themes

python_script:

# Text to speech
#tts:
#  - platform: google_translate
#    language: es-ES
#    cache: true
# cache_dir: /tts
#service_name: google_say

# enables `wake_on_lan` integration
wake_on_lan:

automation: !include automations.yaml
script: !include scripts.yaml
scene: !include scenes.yaml
shell_command: !include shell_commands.yaml
rest_command: !include rest_commands.yaml

#homeassistant:
#  internal_url: "http://*********:8123"
#  external_url: "https://5vfdyv0a68c5lo3dfsushl0syc3gl5zr.ui.nabu.casa:8123"

#homeassistant:
#  allowlist_external_dirs:
#    - /config/www/

powercalc:
  enable_autodiscovery: true

logger:
  default: info

scrape:
  - resource: "https://coinmarketcap.com/currencies/ethereum/"
    scan_interval: 300
    headers:
      User-Agent: Mozilla/5.0
    sensor:
      - name: "ETH Price (CoinMarketCap)"
        select: "div.priceValue"
        unit_of_measurement: "USD"
        value_template: >
          {% set raw_value = value | regex_findall_index('([0-9.,]+)') %}
          {{ raw_value.replace(',', '') }}

sensor:
  - platform: template
    sensors:
      forecast_temperature:
        friendly_name: "Forecast Temperature"
        unit_of_measurement: "°C"
        value_template: "{{ state_attr('weather.forecast_home_2', 'temperature') }}"
    # Optionally use a regex pattern to strip off the currency symbol
    # E.g. extracting just the numeric portion:
    # regex: '.*?([0-9.,]+).*'
# logger:
#   default: info
#   logs:
#     pynetgear: debug
#     homeassistant.components.device_tracker: debug

#zha:
#  device_config:
#    70:b3:d5:2b:60:01:4e:3d-1:
#      type: switch
#    70:b3:d5:2b:60:01:4e:3d-2:
#      type: switch
#    70:b3:d5:2b:60:01:4e:3d-3:
#      type: switch
#    70:b3:d5:2b:60:01:4e:3d-4:
#      type: switch
#    70:b3:d5:2b:60:01:4e:3d-5:
#      type: switch
# Dont need this anymore
# custom_quirks_path: /config/custom_zha_quirks/

#switch:
#- platform: template
#  switches:
#    ventilador_1:
#      unique_id: "Ventilador Juegos"
#      friendly_name: "Ventilador Juegos"
#      value_template: "{{ is_state('input_boolean.ventilador', 'on') }}"
#      turn_on:
#        service: input_boolean.turn_on
#        target:
#          entity_id: input_boolean.ventilador
#      turn_off:
#        service: input_boolean.turn_off
#        target:
#          entity_id: input_boolean.ventilador
#- platform: template
#  switches:
#    ventilador_2:
#      unique_id: "Ventilador Habitacion"
#      friendly_name: "Ventilador Habitacion"
#      value_template: "{{ is_state('input_boolean.ventilador_bed', 'on') }}"
#      turn_on:
#        service: input_boolean.turn_on
#        target:
#          entity_id: input_boolean.ventilador_bed
#      turn_off:
#        service: input_boolean.turn_off
#        target:
#          entity_id: input_boolean.ventilador_bed
#- platform: wake_on_lan
#  name: Ubuntu
#  host: *********
#  mac: 84:47:09:1F:7F:71
#  turn_off:
#    service: shell_command.turn_off_remote_ubuntu

light:
  - platform: template
    lights:
      habitacion_bombillo:
        unique_id: "habitacion_bombillo"
        friendly_name: "Habitacion - Bombillo"
        value_template: "{{ is_state('input_boolean.habitacion_bombilo_input', 'on') }}"
        turn_on:
          service: script.fingerbot_giro_right
        turn_off:
          service: script.fingerbot_giro_left
        supports_transition_template: "{{ false }}"
# template:
#   - sensor:
#       - name: "Persons at Home"
#         unit_of_measurement: ""
#         state: >
#           {% set summary = state_attr('image_processing.doods_10_0_0_25', 'summary') %}

#           {% if summary.person is defined %}
#             {% set persons = summary.person %}
#             {{ persons | int(0) }}
#           {% else %}
#             0
#           {% endif %}

#   - sensor:
#     - name: "Dogs at Home"
#       unit_of_measurement: ""
#       state: >
#         {% set summary = state_attr('image_processing.doods_10_0_0_25', 'summary') %}

#           {% if summary.dog is defined %}
#           {% set dog = summary.dog %}
#           {{ dog | int(0) }}
#         {% else %}
#           0
#         {% endif %}

# image_processing:
#   - platform: doods
#     scan_interval: 5
#     source:
#       - entity_id: camera.10_0_0_25
#     url: "http://0.0.0.0:8080"
#     timeout: 60
#     detector: tensorflow
#     auth_key: 2up3rL0ng4uthK3y
#     file_out:
#       - "/config/www/doods.jpg"
#     confidence: 60
#     labels:
#       - name: dog
#         confidence: 70
#       - name: person
#         confidence: 60
#       - wine glass
#       - cup
#       - knife
#       - spoon
#       - bowl
#       - pizza
#       - couch
#       - bed
#       - dining table
#       - mouse
#       - keyboard
#       - oven
#       - refrigerator
#       - book
#       - clock
#area:
# Exclude top 10% of image
#top: 0.1
# Exclude right 15% of image
#right: 0.85
# Any part of the detection inside this area will trigger
#covers: false

# - platform: flux
#   lights:
#     - light.cocina_parrilla_bombillo
#   name: Fluxer
#   start_time: "18:00"
#   stop_time: "23:00"
#   start_colortemp: 6000
#   sunset_colortemp: 4000
#   stop_colortemp: 4000
#   brightness: 200
#   disable_brightness_adjust: true
#   mode: xy
#   transition: 20
#   interval: 60

# - platform: wake_on_lan
#   mac: "84:    mac: "84:47:09:1f:7f:71"
#   47:09:1f:7f:71"
#   name: "Computador Linux"
#   host: ********7
#   broadcast_address: *********5

# Device Tracker Configuration
device_tracker:
  # Ping device tracker for Daniel Gómez Rico (Android Pixel 9 Pro optimized)
  - platform: ping
    hosts:
      daniel_ping: ********6
    count: 5
    scan_interval: 60
    consider_home: 900

# Previous netgear configuration (commented out)
# device_tracker:
#   - platform: netgear
#     host: ********
#     port: 80
#     username: admin
#     password: Admin123**
#     interval_seconds: 1
#     consider_home: 10
#     accesspoints: false
#     new_device_defaults:
#       track_new_devices: true

# template:
#   - sensor:
#       - name: "Ventilador"
#         state: >
#           {% if is_state('input_boolean.ventilador', 'off') %}
#             off
#           {% elif is_state('input_boolean.ventilador', 'on') %}
#             on
#           {% else %}
#             failed
#           {% endif %}

