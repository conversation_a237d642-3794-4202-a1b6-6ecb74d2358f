# Device Tracking Setup for <PERSON>

## Overview

This document explains the ping-based device tracking configuration for Daniel Gómez Rico using IP address *********.

## Configuration Details

### Ping Device Tracker Configuration

The following configuration has been added to `configuration.yaml`:

```yaml
device_tracker:
  - platform: ping
    hosts:
      daniel_ping: *********
    count: 3
    scan_interval: 30
    consider_home: 180
```

### Configuration Parameters Explained

- **platform: ping** - Uses the ping integration to detect device presence
- **hosts** - Defines the devices to track
  - **daniel_ping** - Friendly name for the device tracker entity
  - ************* - IP address to ping for <PERSON>'s device
- **count: 3** - Number of ping packets to send (default: 1, recommended: 3 for reliability)
- **scan_interval: 30** - How often to ping in seconds (30 seconds = good balance of responsiveness vs network load)
- **consider_home: 180** - How long to wait (in seconds) before marking as "away" after last successful ping (3 minutes)

## Entity Information

### Device Tracker Entity
- **Entity ID**: `device_tracker.daniel_ping`
- **Friendly Name**: "daniel_ping"
- **States**: 
  - `home` - When IP ********* responds to ping
  - `not_home` - When IP ********* doesn't respond to ping

### Person Entity
- **Entity ID**: `person.daniel_gomez_rico`
- **Name**: "Daniel Gómez Rico"
- **Current Device Trackers**:
  - `device_tracker.daniels_macbook_pro`
  - `device_tracker.pixel_9_pro`
  - `device_tracker.daniel_ping` (to be added after restart)

## Setup Steps

### 1. Restart Home Assistant
After adding the configuration, restart Home Assistant to create the new device tracker entity.

### 2. Associate with Person Entity
After restart, you need to associate the new device tracker with Daniel's person entity:

1. Go to **Settings** → **People**
2. Click on **Daniel Gómez Rico**
3. In the **Device trackers** section, add `device_tracker.daniel_ping`
4. Click **Update**

### 3. Verify Operation
- Check that `device_tracker.daniel_ping` appears in **Developer Tools** → **States**
- Verify the state changes between `home` and `not_home` based on device connectivity
- Confirm that Daniel's person entity reflects the combined status of all device trackers

## How It Works

### Presence Detection Logic
1. Home Assistant pings ********* every 30 seconds
2. Sends 3 ping packets per scan for reliability
3. If any ping succeeds → device is `home`
4. If all pings fail → starts countdown timer
5. After 180 seconds of failed pings → device is `not_home`

### Person Entity Logic
Daniel's person entity will be `home` if ANY of his device trackers are `home`:
- MacBook Pro OR
- Pixel 9 Pro OR  
- Ping device (*********)

This provides robust presence detection with multiple fallback methods.

## Network Requirements

### Firewall Configuration
Ensure the device at *********:
- Responds to ICMP ping requests
- Is not blocking pings from Home Assistant server
- Has a static IP or DHCP reservation for *********

### Troubleshooting
If the device tracker shows as `not_home` when it should be `home`:

1. **Test ping manually** from Home Assistant host:
   ```bash
   ping -c 3 *********
   ```

2. **Check device firewall** - Ensure ICMP is allowed

3. **Verify IP address** - Confirm device is actually using *********

4. **Check Home Assistant logs** for ping-related errors:
   ```
   Settings → System → Logs
   ```

## Benefits of This Setup

### Reliability
- Multiple device trackers provide redundancy
- Ping is simple and works with any IP-connected device
- 3-minute timeout prevents false "away" states from temporary network issues

### Performance
- Minimal network overhead (small ping packets every 30 seconds)
- No authentication required
- Works across different device types and operating systems

### Privacy
- Only uses IP connectivity, no personal data transmitted
- Works without installing apps or accessing device APIs

## Customization Options

### Adjust Timing
To make presence detection more/less sensitive, modify these values:

```yaml
# More responsive (checks every 15 seconds, 1 minute timeout)
scan_interval: 15
consider_home: 60

# More conservative (checks every 60 seconds, 5 minute timeout)  
scan_interval: 60
consider_home: 300
```

### Add More Devices
To track additional IP addresses for Daniel:

```yaml
device_tracker:
  - platform: ping
    hosts:
      daniel_ping: *********
      daniel_laptop: *********
      daniel_tablet: *********
```

## Security Considerations

- Ping tracking only reveals presence/absence, not device details
- No authentication credentials stored
- Uses standard ICMP protocol
- Consider network segmentation if privacy is a concern

---

**Note**: Remember to restart Home Assistant and associate the new device tracker with Daniel's person entity to complete the setup.
